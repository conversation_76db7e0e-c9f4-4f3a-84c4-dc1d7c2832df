:root {
    --main-bg: #DCDED0;
    --secondary: #05796C;
    --black: #000000;
    --gray: #979797;
}

/* :root {
    --main-bg: #e5eaed;
    --secondary: #42415c;
    --black: #000000;
    --gray: #979797;
} */

/* :root {
    --main-bg: #f6f7f9;
    --secondary: #70859d;
    --black: #000000;
    --gray: #979797;
} */

/* :root {
    --main-bg: #f3fbf9;
    --secondary: #4ebfb4;
    --black: #000000;
    --gray: #979797;
} */

/* :root {
    --main-bg: #adb6c0;
    --secondary: #4e4459;
    --black: #000000;
    --gray: #979797;
} */

/* :root {
    --main-bg: #eceff5;
    --secondary: #3f3665;
    --black: #000000;
    --gray: #979797;
} */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    user-select: none;
    cursor: url('target.png'), crosshair !important;
}

html {
    height: 100vh;
    width: 100vw;
    font-family: "Instrument Sans", sans-serif;
    font-size: 16px;
    scroll-behavior: smooth;
}

body::before {
    content: "";
    position: fixed;
    inset: 0;
    background-image: url('https://grainy-gradients.vercel.app/noise.svg'); /* or grain.svg */
    opacity: 0.6; /* Adjust intensity */
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: multiply;
  }

body {
    height: 100%;
    width: 100%;
}

.navbar {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    height: 8%;
    padding: 1rem;
    position: relative;
    background-color: var(--main-bg);
}

.logo {
    justify-self: start;
    color: var(--secondary);
    font-size: 1.5rem;
    font-weight: bold;
  }

  .nav-links {
    justify-self: center;
    list-style: none;
    display: flex;
    gap: 1.5rem;
  }

.nav-links li a {
    color: var(--black);
    text-decoration: underline;
    text-decoration-color: var(--gray);
    text-underline-offset: 0.6rem;
    font-size: 1.2rem;
    transition: all 0.5s ease;
}

.active {
    text-decoration-color: var(--black) !important;
}

.nav-links li a:hover {
    text-decoration-color: var(--black) !important;
}

.social-links {
    justify-self: end;
    list-style: none;
    display: flex;
    gap: 1rem;
  }

.social-links li a {
    display: inline-block;
    color: var(--black);
    font-weight: bold;
    font-size: 1.2rem;
    text-decoration: none;
    transition: all 0.5s ease;
}

.social-links li a:hover {
    transform: scale(1.2);
}

.hero {
    display: flex;
    width: 100%;
    height: 65%;
    background-color: var(--main-bg);
}

.section-1 {
    height: 100%;
    width: 30%;
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.rotating-text {
    animation: spin 20s linear infinite;
    transform-origin: 50% 50%;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}


.circle-container {
    position: relative;
    width: 18.75rem;   /* 300px */
    height: 18.75rem;
    transform: scale(1.2);
    align-self: center;
}

.center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2.5rem;   /* 40px */
    height: 2.5rem;
    background-color: var(--secondary);
    border-radius: 50%;
    z-index: 1;
    transition: all 0.5s ease;
}

.center-dot:hover {
    width: 3.5rem;
    height: 3.5rem;
    transform: translate(-50%, -50%) scale(1.4);
}

text {
    font-size: 0.935em;
    letter-spacing: 0.23em;
    fill: var(--black);
}

.hero-text {
    padding: 2.5rem; /* 40px */
    margin-left: 1.2rem;
}

.subtitle {
    font-weight: bold;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.625rem; /* 10px */
}

.line {
    flex-grow: 1;
    height: 0.125rem; /* 2px */
    background-color: var(--black);
}

.main-text .top {
    font-size: 3.75rem; /* 60px */
    font-weight: 300;
    color: var(--black);
    margin-top: 1.25rem; /* 20px */
}

.main-text .bottom {
    font-size: 3.75rem; /* 60px */
    font-weight: 900;
    color: var(--secondary);
}

.section-2 {
    height: 100%;
    width: 30%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.image-stack {
    position: relative;
    width: fit-content;
}

:root {
    --image-stack-width: 19.5rem;
    --image-stack-height: 20.8rem;

    --back-box-top: calc(var(--image-stack-height) * -0.4);
    --back-box-left: calc(var(--image-stack-width) * -0.5);
  
    --front-image-top: calc(var(--image-stack-height) * -0.85);
    --front-image-left: calc(var(--image-stack-width) * -0.25);
}
  
.back-box {
    position: absolute;
    top: var(--back-box-top);
    left: var(--back-box-left);
    width: var(--image-stack-width);
    height: var(--image-stack-height);
    background-color: var(--secondary);
}

.front-image {
    position: absolute;
    top: var(--front-image-top);
    left: var(--front-image-left);
    width: var(--image-stack-width);
    height: var(--image-stack-height);
    object-fit: cover;
}

.back-box,
.front-image {
  transition: top 0.3s ease, left 0.3s ease;
}

.image-stack:hover .back-box {
  top: calc(var(--back-box-top) - 2rem);
  left: calc(var(--back-box-left) + 0.75rem);
}

.image-stack:hover .front-image {
  top: calc(var(--front-image-top) + 2rem);
  left: calc(var(--front-image-left) - 0.75rem);
}


.section-3 {
    height: 100%;
    width: 40%;
    padding: 4rem 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

  .intro-box h1 {
    font-size: 3.25rem;
    font-weight: 200;
    margin-bottom: 1.2rem;
  }

  .intro-box h1 span {

    font-weight: 500;
  }
  
  .underline {
    text-decoration: underline;
    text-underline-offset: 0.4rem;
    text-decoration-thickness: 2px;
  }

  .intro-box h5 {
    font-size: 1.25rem;
    font-weight: 200;
    line-height: 1.75;
    color: var(--secondary);
    margin-bottom: 2rem;
  }
  
  .intro-box p {

    font-size: 1.75rem;
    font-weight: 200;
    line-height: 1.75;
    color: var(--black);
    margin-bottom: 2rem;
  }

  .more {
    width: fit-content;
    align-self: center;
    border: 0.125rem solid var(--black);
    padding: 0.75rem;
    background-color: var(--secondary);
    font-size: 0.9rem; /* 20px */
    color: var(--main-bg);
    cursor: pointer;
}

.more svg {
    transform: translateY(0.15rem) scale(1.1);
}

.skill-banner {
    width: 100%;
    background-color: var(--secondary);
}

.skill-icons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    padding: 2rem;
}

.skill-icons h3 {
    font-size: 1.25rem;
    color: var(--main-bg);
    font-weight: 200;
    text-align: center;
}

.icons {
    width: 100%;
    padding: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-around;
    list-style: none;
}

.icon-with-name {
    height: 6rem;
    display: flex;
    min-width: 6rem;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}

.icon-name {
    font-size: 1rem;
    color: var(--main-bg);
    font-weight: 200;
}

.work {
    width: 100%;
    background-color: var(--main-bg);
    padding: 0.75rem 2rem;
}

.work h3 {
    font-size: 1.75rem;
    color: var(--secondary);
    font-weight: 800;
    text-align: center;
}

.projects {
    display: flex;
    flex-wrap: wrap;
    gap: 5rem 0rem;
    justify-content: space-evenly;
    padding: 2rem 0rem;
  }
  
  .project {
    position: relative;
    width: 40rem;
    overflow: hidden;
    border: 1px solid var(--secondary);
  }
  
  .project::before {
    content: "";
    position: absolute;
    inset: 0;
    background-color: var(--secondary);
    opacity: 0.9;
    transition: opacity 0.1s ease;
    z-index: 1;
    pointer-events: none;
  }
  
  .project:hover::before {
    opacity: 0;
  }
  
  .project img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    position: relative;
    z-index: 0; /* behind the overlay */
  }
  
  .project-details {
    padding: 1rem;
  }
  
  .project-details h4 {
    font-size: 2rem;
    margin-bottom: 0.3rem;
    color: var(--main-bg);
  }
  
  .tooltip {
    position: fixed;
    background-color: var(--black);
    color: var(--main-bg);
    padding: 1rem 1.2rem;
    font-size: 1rem;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 9999;
    width: 30rem;
    border: 1px solid var(--main-bg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  }
  
  .tooltip-body {
    margin-bottom: 0.5rem;
  }
  
  .tooltip-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0.5rem;
  }
  
  .tech-tile {
    background-color: var(--black);
    border: 1px solid var(--main-bg);
    color: var(--main-bg);
    padding: 0.3rem 0.6rem;
    border-radius: 0.15rem;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .tooltip-footer {
    font-size: 0.85rem;
    opacity: 0.85;
  }
  
  .awards-marquee {
    background-color: var(--secondary);
    color: var(--main-bg);
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    padding: 0.7rem 0;
    border-top: 1px solid var(--main-bg);
    border-bottom: 1px solid var(--main-bg);
  }
  
  .awards-track {
    display: inline-block;
    padding-left: 100%;
    animation: scroll-left 18s linear infinite;
  }

  .awards-marquee:hover .awards-track {
    animation-play-state: paused;
  }
  
  .awards-track span {
    display: inline-block;
    margin-right: 3rem;
    font-size: 3rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.2rem;
  }
  
  @keyframes scroll-left {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-100%);
    }
  }
  
  .contact-section {
    background-color: var(--main-bg);
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .contact-section h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--secondary);
  }
  
  .contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    width: 80%;
  }

  .form-row {
    display: flex;
    gap: 1rem;
  }
  
  .form-row input {
    flex: 1;
  }
  
  .contact-form input,
  .contact-form textarea {
    padding: 0.8rem 1rem;
    border: 2px solid var(--secondary);
    background-color: var(--main-bg);
    color: black;
    font-size: 1rem;
    resize: vertical;
  }
  
  .contact-form input::placeholder,
  .contact-form textarea::placeholder {
    color: var(--gray);
  }
  
  .contact-form button {
    width: fit-content;
    align-self: center;
    border: 0.125rem solid var(--black);
    padding: 0.75rem;
    background-color: var(--secondary);
    font-size: 1rem;
    color: var(--main-bg);
  }

  .site-footer {
    background-color: var(--secondary);
    color: var(--main-bg);
    text-align: center;
    padding: 1.2rem 1rem;
    font-size: 0.9rem;
    letter-spacing: 0.1rem;
  }